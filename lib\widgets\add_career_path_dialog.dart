/// حوار إضافة مسار وظيفي جديد
/// يوفر واجهة شاملة لإنشاء المسارات الوظيفية مع جميع المستويات والمتطلبات
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../constants/revolutionary_design_colors.dart';

class AddCareerPathDialog extends StatefulWidget {
  final Function(CareerPath) onSave;

  const AddCareerPathDialog({
    super.key,
    required this.onSave,
  });

  @override
  State<AddCareerPathDialog> createState() => _AddCareerPathDialogState();
}

class _AddCareerPathDialogState extends State<AddCareerPathDialog> {
  final _formKey = GlobalKey<FormState>();
  final _pathNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  int? _selectedDepartmentId;
  final List<CareerLevel> _levels = [];
  final List<String> _requirements = [];
  bool _isActive = true;

  final List<Map<String, dynamic>> _departments = [
    {'id': 1, 'name': 'المحاسبة والمالية'},
    {'id': 2, 'name': 'تقنية المعلومات'},
    {'id': 3, 'name': 'الموارد البشرية'},
    {'id': 4, 'name': 'المبيعات والتسويق'},
    {'id': 5, 'name': 'العمليات والإنتاج'},
    {'id': 6, 'name': 'الإدارة العامة'},
  ];

  @override
  void initState() {
    super.initState();
    _addDefaultLevel();
  }

  @override
  void dispose() {
    _pathNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfo(),
                      const SizedBox(height: 24),
                      _buildLevelsSection(),
                      const SizedBox(height: 24),
                      _buildRequirementsSection(),
                      const SizedBox(height: 24),
                      _buildStatusSection(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.successGlow.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.add_road,
            color: RevolutionaryColors.successGlow,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إضافة مسار وظيفي',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'إنشاء مسار وظيفي جديد مع المستويات والمتطلبات',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المعلومات الأساسية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _pathNameController,
          decoration: InputDecoration(
            labelText: 'اسم المسار الوظيفي *',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(Icons.work),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم المسار الوظيفي';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<int>(
          value: _selectedDepartmentId,
          decoration: InputDecoration(
            labelText: 'القسم',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(Icons.business),
          ),
          items: _departments.map((dept) {
            return DropdownMenuItem(
              value: dept['id'] as int,
              child: Text(dept['name'] as String),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedDepartmentId = value;
            });
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'وصف المسار',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(Icons.description),
          ),
        ),
      ],
    );
  }

  Widget _buildLevelsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Expanded(
              child: Text(
                'المستويات الوظيفية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            ElevatedButton.icon(
              onPressed: _addLevel,
              icon: const Icon(Icons.add, size: 16),
              label: const Text('إضافة مستوى'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.infoTurquoise,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_levels.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'لا توجد مستويات وظيفية\nاضغط "إضافة مستوى" لإضافة مستوى جديد',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _levels.length,
            itemBuilder: (context, index) {
              return _buildLevelCard(index);
            },
          ),
      ],
    );
  }

  Widget _buildLevelCard(int index) {
    final level = _levels[index];
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'المستوى ${index + 1}: ${level.levelName}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _editLevel(index),
                  icon: const Icon(Icons.edit, size: 20),
                ),
                IconButton(
                  onPressed: () => _removeLevel(index),
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('الراتب: ${level.minSalary} - ${level.maxSalary} ل.س'),
            if (level.description != null) ...[
              const SizedBox(height: 4),
              Text('الوصف: ${level.description}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Expanded(
              child: Text(
                'المتطلبات العامة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            ElevatedButton.icon(
              onPressed: _addRequirement,
              icon: const Icon(Icons.add, size: 16),
              label: const Text('إضافة متطلب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.warningAmber,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_requirements.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'لا توجد متطلبات عامة\nاضغط "إضافة متطلب" لإضافة متطلب جديد',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _requirements.asMap().entries.map((entry) {
              final index = entry.key;
              final requirement = entry.value;
              return Chip(
                label: Text(requirement),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeRequirement(index),
                backgroundColor: RevolutionaryColors.warningAmber.withValues(alpha: 0.1),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Row(
      children: [
        Switch(
          value: _isActive,
          onChanged: (value) {
            setState(() {
              _isActive = value;
            });
          },
          activeColor: RevolutionaryColors.successGlow,
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مسار نشط',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Text(
                'يمكن للموظفين التقدم لهذا المسار',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _savePath,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.successGlow,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: const Text('حفظ المسار', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  void _addDefaultLevel() {
    _levels.add(CareerLevel(
      levelName: 'مستوى مبتدئ',
      description: 'المستوى الأول في المسار الوظيفي',
      minSalary: 800000,
      maxSalary: 1200000,
      requiredExperience: 0,
      requiredSkills: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ));
  }

  void _addLevel() {
    showDialog(
      context: context,
      builder: (context) => _LevelDialog(
        onSave: (level) {
          setState(() {
            _levels.add(level);
          });
        },
      ),
    );
  }

  void _editLevel(int index) {
    showDialog(
      context: context,
      builder: (context) => _LevelDialog(
        level: _levels[index],
        onSave: (level) {
          setState(() {
            _levels[index] = level;
          });
        },
      ),
    );
  }

  void _removeLevel(int index) {
    setState(() {
      _levels.removeAt(index);
    });
  }

  void _addRequirement() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('إضافة متطلب'),
          content: TextFormField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'المتطلب',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  setState(() {
                    _requirements.add(controller.text.trim());
                  });
                  Navigator.of(context).pop();
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  void _removeRequirement(int index) {
    setState(() {
      _requirements.removeAt(index);
    });
  }

  void _savePath() {
    if (_formKey.currentState!.validate()) {
      if (_levels.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب إضافة مستوى واحد على الأقل'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final path = CareerPath(
        pathName: _pathNameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        departmentId: _selectedDepartmentId,
        levels: _levels,
        requirements: _requirements,
        isActive: _isActive,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(path);
      Navigator.of(context).pop();
    }
  }
}

class _LevelDialog extends StatefulWidget {
  final CareerLevel? level;
  final Function(CareerLevel) onSave;

  const _LevelDialog({
    this.level,
    required this.onSave,
  });

  @override
  State<_LevelDialog> createState() => _LevelDialogState();
}

class _LevelDialogState extends State<_LevelDialog> {
  final _formKey = GlobalKey<FormState>();
  final _levelNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _minSalaryController = TextEditingController();
  final _maxSalaryController = TextEditingController();
  final _experienceController = TextEditingController();

  List<String> _skills = [];

  @override
  void initState() {
    super.initState();
    if (widget.level != null) {
      _levelNameController.text = widget.level!.levelName;
      _descriptionController.text = widget.level!.description ?? '';
      _minSalaryController.text = widget.level!.minSalary.toString();
      _maxSalaryController.text = widget.level!.maxSalary.toString();
      _experienceController.text = widget.level!.requiredExperience.toString();
      _skills = List.from(widget.level!.requiredSkills);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.level == null ? 'إضافة مستوى' : 'تعديل مستوى'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _levelNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المستوى *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم المستوى';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف المستوى',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _minSalaryController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأدنى للراتب *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (double.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _maxSalaryController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأقصى للراتب *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (double.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _experienceController,
                  decoration: const InputDecoration(
                    labelText: 'سنوات الخبرة المطلوبة',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveLevel,
          child: const Text('حفظ'),
        ),
      ],
    );
  }

  void _saveLevel() {
    if (_formKey.currentState!.validate()) {
      final level = CareerLevel(
        levelName: _levelNameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        minSalary: double.parse(_minSalaryController.text),
        maxSalary: double.parse(_maxSalaryController.text),
        requiredExperience: int.tryParse(_experienceController.text) ?? 0,
        requiredSkills: _skills,
        createdAt: widget.level?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(level);
      Navigator.of(context).pop();
    }
  }
}
