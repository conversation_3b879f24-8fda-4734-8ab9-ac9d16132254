class AppConstants {
  // معلومات التطبيق
  static const String appName = 'Smart Ledger';
  static const String appNameArabic = 'دفتر الحسابات الذكي';
  static const String appVersion = '1.0.0';
  static const String developerName = 'مجد محمد زياد يسير';

  // قاعدة البيانات
  static const String databaseName = 'smart_ledger.db';
  static const int databaseVersion = 10;

  // أسماء الجداول
  static const String accountsTable = 'accounts';
  static const String journalEntriesTable = 'journal_entries';
  static const String journalEntryDetailsTable = 'journal_entry_details';
  static const String invoicesTable = 'invoices';
  static const String invoiceItemsTable = 'invoice_items';
  static const String customersTable = 'customers';
  static const String suppliersTable = 'suppliers';
  static const String itemsTable = 'items';
  static const String currenciesTable = 'currencies';
  static const String taxesTable = 'taxes';
  static const String settingsTable = 'settings';
  static const String auditLogTable = 'audit_log';

  // جداول نظام المستخدمين والصلاحيات
  static const String usersTable = 'users';
  static const String rolesTable = 'roles';
  static const String permissionsTable = 'permissions';
  static const String userRolesTable = 'user_roles';
  static const String rolePermissionsTable = 'role_permissions';
  static const String userSessionsTable = 'user_sessions';

  // جداول نظام الفواتير المتقدم
  static const String paymentsTable = 'payments';
  static const String paymentAllocationsTable = 'payment_allocations';
  static const String invoiceStatusHistoryTable = 'invoice_status_history';
  static const String quotationsTable = 'quotations';
  static const String quotationItemsTable = 'quotation_items';
  static const String recurringInvoicesTable = 'recurring_invoices';
  static const String invoiceTemplatesTable = 'invoice_templates';

  // جداول نظام المخزون المتقدم
  static const String warehousesTable = 'warehouses';
  static const String locationsTable = 'locations';
  static const String stockMovementsTable = 'stock_movements';
  static const String inventoryCountsTable = 'inventory_counts';
  static const String inventoryCountItemsTable = 'inventory_count_items';
  static const String itemLocationsTable = 'item_locations';

  // جداول نظام الموارد البشرية (HR)
  static const String employeesTable = 'employees';
  static const String departmentsTable = 'departments';
  static const String positionsTable = 'positions';
  static const String employeeContractsTable = 'employee_contracts';
  static const String salariesTable = 'salaries';
  static const String salaryDetailsTable = 'salary_details';
  static const String attendanceTable = 'attendance';
  static const String leavesTable = 'leaves';
  static const String loansTable = 'loans';
  static const String loanInstallmentsTable = 'loan_installments';
  static const String employeeDocumentsTable = 'employee_documents';
  static const String payrollTable = 'payroll';
  static const String payrollDetailsTable = 'payroll_details';
  static const String salaryComponentsTable = 'salary_components';

  // جداول HR المتقدمة
  static const String salaryTemplatesTable = 'salary_templates';
  static const String trainingProgramsTable = 'training_programs';
  static const String trainingSessionsTable = 'training_sessions';
  static const String trainingEnrollmentsTable = 'training_enrollments';
  static const String performanceCyclesTable = 'performance_cycles';
  static const String evaluationCriteriaTable = 'evaluation_criteria';
  static const String performanceEvaluationsTable = 'performance_evaluations';
  static const String evaluationDetailsTable = 'evaluation_details';
  static const String careerPathsTable = 'career_paths';
  static const String careerDevelopmentPlansTable = 'career_development_plans';
  static const String careerReviewsTable = 'career_reviews';

  // جداول إضافية لنظام HR
  static const String approvalsTable = 'approvals';
  static const String documentsTable = 'employee_documents';
  static const String notificationsTable = 'notifications';

  // جداول نظام جدولة الدفعات
  static const String paymentSchedulesTable = 'payment_schedules';
  static const String paymentRemindersTable = 'payment_reminders';
  static const String paymentNotificationsTable = 'payment_notifications';

  // أنواع الحسابات
  static const String accountTypeAsset = 'asset';
  static const String accountTypeLiability = 'liability';
  static const String accountTypeEquity = 'equity';
  static const String accountTypeRevenue = 'revenue';
  static const String accountTypeExpense = 'expense';
  static const String accountTypePurchase = 'purchase';
  static const String accountTypeSale = 'sale';
  static const String accountTypeInventory = 'inventory';

  // أنواع القيود
  static const String entryTypeGeneral = 'general';
  static const String entryTypeSale = 'sale';
  static const String entryTypePurchase = 'purchase';
  static const String entryTypePayment = 'payment';
  static const String entryTypeReceipt = 'receipt';

  // أنواع الفواتير
  static const String invoiceTypeSale = 'sale';
  static const String invoiceTypePurchase = 'purchase';
  static const String invoiceTypeSaleReturn = 'sale_return';
  static const String invoiceTypePurchaseReturn = 'purchase_return';

  // حالات الفواتير
  static const String invoiceStatusDraft = 'draft';
  static const String invoiceStatusConfirmed = 'confirmed';
  static const String invoiceStatusPaid = 'paid';
  static const String invoiceStatusCancelled = 'cancelled';

  // العملات
  static const String currencyCodeSYP = 'SYP'; // الليرة السورية
  static const String currencyCodeUSD = 'USD'; // الدولار الأمريكي
  static const String currencyCodeEUR = 'EUR'; // اليورو

  // أسماء العملات بالعربية
  static const Map<String, String> currencyNames = {
    'SYP': 'ليرة سورية',
    'USD': 'دولار أمريكي',
    'EUR': 'يورو',
  };

  // رموز العملات
  static const Map<String, String> currencySymbols = {
    'SYP': 'ل.س',
    'USD': '\$',
    'EUR': '€',
  };

  // تنسيق التواريخ
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';

  // تنسيق الأرقام
  static const String numberFormat = '#,##0.00';
  static const String currencyFormat = '#,##0.00';

  // الإعدادات الافتراضية
  static const String defaultCurrency = currencyCodeSYP;
  static const String defaultLanguage = 'ar';
  static const bool defaultRTL = true;

  // حدود التطبيق
  static const int maxAccountNameLength = 100;
  static const int maxDescriptionLength = 500;
  static const int maxItemNameLength = 100;
  static const int maxCustomerNameLength = 100;
  static const int maxSupplierNameLength = 100;

  // رسائل التحقق
  static const String requiredFieldMessage = 'هذا الحقل مطلوب';
  static const String invalidNumberMessage = 'يرجى إدخال رقم صحيح';
  static const String invalidEmailMessage = 'يرجى إدخال بريد إلكتروني صحيح';
  static const String invalidPhoneMessage = 'يرجى إدخال رقم هاتف صحيح';

  // رسائل النجاح
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';

  // رسائل الخطأ
  static const String saveErrorMessage = 'حدث خطأ أثناء الحفظ';
  static const String updateErrorMessage = 'حدث خطأ أثناء التحديث';
  static const String deleteErrorMessage = 'حدث خطأ أثناء الحذف';
  static const String loadErrorMessage = 'حدث خطأ أثناء تحميل البيانات';

  // أنواع عمليات المراجعة (Audit Actions)
  static const String auditActionCreate = 'CREATE';
  static const String auditActionUpdate = 'UPDATE';
  static const String auditActionDelete = 'DELETE';
  static const String auditActionLogin = 'LOGIN';
  static const String auditActionLogout = 'LOGOUT';
  static const String auditActionPasswordChange = 'PASSWORD_CHANGE';
  static const String auditActionInvoicePost = 'INVOICE_POST';
  static const String auditActionJournalPost = 'JOURNAL_POST';
  static const String auditActionBackup = 'BACKUP';
  static const String auditActionRestore = 'RESTORE';
  static const String auditActionExport = 'EXPORT';
  static const String auditActionImport = 'IMPORT';

  // أنواع الكيانات للمراجعة (Audit Entity Types)
  static const String auditEntityAccount = 'ACCOUNT';
  static const String auditEntityCustomer = 'CUSTOMER';
  static const String auditEntitySupplier = 'SUPPLIER';
  static const String auditEntityItem = 'ITEM';
  static const String auditEntityInvoice = 'INVOICE';
  static const String auditEntityJournalEntry = 'JOURNAL_ENTRY';
  static const String auditEntityCurrency = 'CURRENCY';
  static const String auditEntityTax = 'TAX';
  static const String auditEntitySettings = 'SETTINGS';
  static const String auditEntityUser = 'USER';
  static const String auditEntitySystem = 'SYSTEM';
  static const String auditEntityTemplate = 'TEMPLATE';

  // أيقونات الحسابات
  static const Map<String, String> accountIcons = {
    accountTypeAsset: '💰',
    accountTypeLiability: '📋',
    accountTypeEquity: '🏛️',
    accountTypeRevenue: '📈',
    accountTypeExpense: '📉',
    accountTypePurchase: '🛒',
    accountTypeSale: '💳',
    accountTypeInventory: '📦',
  };

  // ألوان الحسابات
  static const Map<String, String> accountColors = {
    accountTypeAsset: '#2E7A5A',
    accountTypeLiability: '#8A5A2E',
    accountTypeEquity: '#2E5A8A',
    accountTypeRevenue: '#2E7A5A',
    accountTypeExpense: '#8A2E2E',
    accountTypePurchase: '#8A2E5A',
    accountTypeSale: '#5A8A2E',
    accountTypeInventory: '#5A2E8A',
  };

  // ثوابت نظام المستخدمين والصلاحيات
  static const String defaultAdminUsername = 'admin';
  static const String defaultAdminPassword = 'admin123';
  static const String defaultAdminFullName = 'مدير النظام';
  static const String defaultAdminEmail = '<EMAIL>';

  // حالات المستخدم
  static const String userStatusActive = 'active';
  static const String userStatusInactive = 'inactive';
  static const String userStatusLocked = 'locked';
  static const String userStatusExpired = 'expired';

  // أنواع الجلسات
  static const String sessionTypeLogin = 'login';
  static const String sessionTypeLogout = 'logout';
  static const String sessionTypeTimeout = 'timeout';

  // إعدادات الأمان
  static const int maxLoginAttempts = 5;
  static const int sessionTimeoutMinutes = 60;
  static const int passwordMinLength = 6;
  static const int passwordMaxLength = 50;

  // أدوار افتراضية
  static const String roleAdmin = 'مدير عام';
  static const String roleAccountant = 'محاسب';
  static const String roleWarehouseKeeper = 'أمين مخزن';
  static const String roleSalesEmployee = 'موظف مبيعات';
  static const String rolePurchaseEmployee = 'موظف مشتريات';
  static const String roleHRManager = 'مدير الموارد البشرية';
  static const String roleHREmployee = 'موظف الموارد البشرية';
  static const String roleUser = 'مستخدم عادي';

  // ثوابت نظام الموارد البشرية
  // حالات الموظفين
  static const String employeeStatusActive = 'active';
  static const String employeeStatusInactive = 'inactive';
  static const String employeeStatusTerminated = 'terminated';
  static const String employeeStatusSuspended = 'suspended';

  // الجنس
  static const String genderMale = 'male';
  static const String genderFemale = 'female';

  // الحالة الاجتماعية
  static const String maritalStatusSingle = 'single';
  static const String maritalStatusMarried = 'married';
  static const String maritalStatusDivorced = 'divorced';
  static const String maritalStatusWidowed = 'widowed';

  // أنواع التوظيف
  static const String employmentTypeFullTime = 'full_time';
  static const String employmentTypePartTime = 'part_time';
  static const String employmentTypeContract = 'contract';
  static const String employmentTypeInternship = 'internship';

  // أنواع العقود
  static const String contractTypePermanent = 'permanent';
  static const String contractTypeTemporary = 'temporary';
  static const String contractTypePartTime = 'part_time';
  static const String contractTypeContract = 'contract';

  // حالات العقود
  static const String contractStatusActive = 'active';
  static const String contractStatusExpired = 'expired';
  static const String contractStatusTerminated = 'terminated';
  static const String contractStatusSuspended = 'suspended';

  // أنواع الإجازات
  static const String leaveTypeAnnual = 'annual';
  static const String leaveTypeSick = 'sick';
  static const String leaveTypeEmergency = 'emergency';
  static const String leaveTypeMaternity = 'maternity';
  static const String leaveTypePaternity = 'paternity';
  static const String leaveTypeUnpaid = 'unpaid';
  static const String leaveTypeStudy = 'study';
  static const String leaveTypePilgrimage = 'pilgrimage'; // إجازة حج
  static const String leaveTypeMarriage = 'marriage'; // إجازة زواج
  static const String leaveTypeDeath = 'death'; // إجازة وفاة
  static const String leaveTypeCompensatory = 'compensatory'; // إجازة تعويضية
  static const String leaveTypeTraining = 'training'; // إجازة تدريب
  static const String leaveTypePersonal = 'personal'; // إجازة شخصية

  // حالات الإجازات
  static const String leaveStatusPending = 'pending';
  static const String leaveStatusApproved = 'approved';
  static const String leaveStatusRejected = 'rejected';
  static const String leaveStatusCancelled = 'cancelled';
  static const String leaveStatusExpired = 'expired';

  // معلومات أنواع الإجازات (الحد الأقصى للأيام)
  static const Map<String, int> leaveTypeMaxDays = {
    leaveTypeAnnual: 30, // 30 يوم سنوياً
    leaveTypeSick: 90, // 90 يوم مرضية سنوياً
    leaveTypeEmergency: 7, // 7 أيام طوارئ
    leaveTypeMaternity: 120, // 120 يوم أمومة
    leaveTypePaternity: 3, // 3 أيام أبوة
    leaveTypeMarriage: 7, // 7 أيام زواج
    leaveTypeDeath: 3, // 3 أيام وفاة
    leaveTypePilgrimage: 30, // 30 يوم حج
    leaveTypeStudy: 365, // سنة دراسية
    leaveTypeTraining: 30, // 30 يوم تدريب
    leaveTypePersonal: 5, // 5 أيام شخصية
    leaveTypeCompensatory: 30, // 30 يوم تعويضية
    leaveTypeUnpaid: 365, // بدون راتب - حسب الحاجة
  };

  // أنواع الإجازات المدفوعة
  static const List<String> paidLeaveTypes = [
    leaveTypeAnnual,
    leaveTypeSick,
    leaveTypeEmergency,
    leaveTypeMaternity,
    leaveTypePaternity,
    leaveTypeMarriage,
    leaveTypeDeath,
    leaveTypePilgrimage,
    leaveTypeCompensatory,
  ];

  // أنواع الإجازات التي تحتاج موافقة خاصة
  static const List<String> specialApprovalLeaveTypes = [
    leaveTypeStudy,
    leaveTypePilgrimage,
    leaveTypeTraining,
    leaveTypeUnpaid,
  ];

  // أنواع القروض والسلف
  static const String loanTypeAdvance = 'advance';
  static const String loanTypeLoan = 'loan';
  static const String loanTypeEmergency = 'emergency';

  // حالات القروض
  static const String loanStatusActive = 'active';
  static const String loanStatusCompleted = 'completed';
  static const String loanStatusCancelled = 'cancelled';
  static const String loanStatusOverdue = 'overdue';

  // أنواع عناصر الراتب
  static const String salaryComponentBasic = 'basic';
  static const String salaryComponentAllowance = 'allowance';
  static const String salaryComponentBonus = 'bonus';
  static const String salaryComponentDeduction = 'deduction';
  static const String salaryComponentTax = 'tax';
  static const String salaryComponentInsurance = 'insurance';
  static const String salaryComponentOvertime = 'overtime';

  // أنواع وثائق الموظفين
  static const String documentTypeId = 'id';
  static const String documentTypePassport = 'passport';
  static const String documentTypeCertificate = 'certificate';
  static const String documentTypeContract = 'contract';
  static const String documentTypeResume = 'resume';
  static const String documentTypeMedical = 'medical';
  static const String documentTypeOther = 'other';

  // الضرائب السورية (شرائح ضريبة الدخل)
  static const double syrianTaxBracket1 = 0.05; // 5% للشريحة الأولى
  static const double syrianTaxBracket2 = 0.10; // 10% للشريحة الثانية
  static const double syrianTaxBracket3 = 0.15; // 15% للشريحة الثالثة
  static const double syrianTaxBracket4 = 0.20; // 20% للشريحة الرابعة

  // حدود الشرائح الضريبية (بالليرة السورية)
  static const double syrianTaxBracket1Limit = 2000000; // 2 مليون ليرة
  static const double syrianTaxBracket2Limit = 4000000; // 4 مليون ليرة
  static const double syrianTaxBracket3Limit = 6000000; // 6 مليون ليرة

  // نسب الضمان الاجتماعي السوري
  static const double syrianSocialInsuranceEmployeeRate = 0.07; // 7% على الموظف
  static const double syrianSocialInsuranceEmployerRate =
      0.14; // 14% على صاحب العمل
  static const double syrianSocialInsuranceMaxSalary =
      10000000; // الحد الأقصى للراتب الخاضع للضمان
}
