import 'dart:io';
import 'dart:convert';
import 'package:sqflite_sqlcipher/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../services/encryption_service.dart';
import '../services/logging_service.dart';
import '../services/visual_report_builder_service.dart';
import '../services/syrian_tax_service.dart';
import '../services/smart_notification_service.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  static String? _currentPassword;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  /// الحصول على قاعدة البيانات مع التشفير
  Future<Database> get database async {
    if (_database == null) {
      throw Exception(
        'قاعدة البيانات غير مهيأة. يرجى استدعاء initializeDatabase أولاً',
      );
    }
    return _database!;
  }

  /// تهيئة قاعدة البيانات مع كلمة المرور
  Future<bool> initializeDatabase(String userPassword) async {
    try {
      // التحقق من كلمة المرور والحصول على مفتاح قاعدة البيانات
      final dbPassword = await EncryptionService.getDatabasePassword(
        userPassword,
      );
      if (dbPassword == null) {
        LoggingService.error(
          'فشل في التحقق من كلمة مرور قاعدة البيانات',
          category: 'Database',
        );
        return false;
      }

      _currentPassword = dbPassword;
      _database = await _initDatabase();

      LoggingService.info(
        'تم تهيئة قاعدة البيانات المشفرة بنجاح',
        category: 'Database',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'فشل في تهيئة قاعدة البيانات',
        category: 'Database',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      _currentPassword = null;

      LoggingService.info('تم إغلاق قاعدة البيانات', category: 'Database');
    }
  }

  Future<Database> _initDatabase() async {
    if (_currentPassword == null) {
      throw Exception('كلمة مرور قاعدة البيانات غير محددة');
    }

    String path = join(await getDatabasesPath(), AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      password: _currentPassword,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول العملات
    await db.execute('''
      CREATE TABLE ${AppConstants.currenciesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        exchange_rate REAL NOT NULL DEFAULT 1.0,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول الحسابات
    await db.execute('''
      CREATE TABLE ${AppConstants.accountsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        parent_id INTEGER,
        level INTEGER NOT NULL DEFAULT 1,
        is_active INTEGER NOT NULL DEFAULT 1,
        balance REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (parent_id) REFERENCES ${AppConstants.accountsTable} (id),
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول القيود المحاسبية
    await db.execute('''
      CREATE TABLE ${AppConstants.journalEntriesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_number TEXT NOT NULL UNIQUE,
        entry_date TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        total_debit REAL NOT NULL DEFAULT 0.0,
        total_credit REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        is_posted INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول تفاصيل القيود
    await db.execute('''
      CREATE TABLE ${AppConstants.journalEntryDetailsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        journal_entry_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        debit_amount REAL NOT NULL DEFAULT 0.0,
        credit_amount REAL NOT NULL DEFAULT 0.0,
        description TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (journal_entry_id) REFERENCES ${AppConstants.journalEntriesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES ${AppConstants.accountsTable} (id)
      )
    ''');

    // جدول العملاء
    await db.execute('''
      CREATE TABLE ${AppConstants.customersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        balance REAL NOT NULL DEFAULT 0.0,
        credit_limit REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول الموردين
    await db.execute('''
      CREATE TABLE ${AppConstants.suppliersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        balance REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول الأصناف
    await db.execute('''
      CREATE TABLE ${AppConstants.itemsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        unit TEXT NOT NULL,
        cost_price REAL NOT NULL DEFAULT 0.0,
        selling_price REAL NOT NULL DEFAULT 0.0,
        quantity REAL NOT NULL DEFAULT 0.0,
        min_quantity REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول الفواتير
    await db.execute('''
      CREATE TABLE ${AppConstants.invoicesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        invoice_date TEXT NOT NULL,
        type TEXT NOT NULL,
        customer_id INTEGER,
        supplier_id INTEGER,
        subtotal REAL NOT NULL DEFAULT 0.0,
        tax_amount REAL NOT NULL DEFAULT 0.0,
        discount_amount REAL NOT NULL DEFAULT 0.0,
        total_amount REAL NOT NULL DEFAULT 0.0,
        paid_amount REAL NOT NULL DEFAULT 0.0,
        remaining_amount REAL NOT NULL DEFAULT 0.0,
        currency_id INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT '${AppConstants.invoiceStatusDraft}',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES ${AppConstants.customersTable} (id),
        FOREIGN KEY (supplier_id) REFERENCES ${AppConstants.suppliersTable} (id),
        FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id)
      )
    ''');

    // جدول عناصر الفواتير
    await db.execute('''
      CREATE TABLE ${AppConstants.invoiceItemsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        discount_percentage REAL NOT NULL DEFAULT 0.0,
        discount_amount REAL NOT NULL DEFAULT 0.0,
        tax_percentage REAL NOT NULL DEFAULT 0.0,
        tax_amount REAL NOT NULL DEFAULT 0.0,
        net_amount REAL NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id)
      )
    ''');

    // جدول الضرائب
    await db.execute('''
      CREATE TABLE ${AppConstants.taxesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        percentage REAL NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول الإعدادات
    await db.execute('''
      CREATE TABLE ${AppConstants.settingsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول سجل المراجعة (Audit Log)
    await db.execute('''
      CREATE TABLE ${AppConstants.auditLogTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        action TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER,
        entity_name TEXT,
        old_values TEXT,
        new_values TEXT,
        user_id TEXT,
        user_name TEXT,
        ip_address TEXT,
        user_agent TEXT,
        session_id TEXT,
        timestamp TEXT NOT NULL,
        description TEXT,
        severity TEXT NOT NULL DEFAULT 'INFO',
        category TEXT,
        reference_type TEXT,
        reference_id INTEGER,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول الأدوار
    await db.execute('''
      CREATE TABLE ${AppConstants.rolesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT NOT NULL,
        permissions TEXT NOT NULL,
        is_active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول المستخدمين
    await db.execute('''
      CREATE TABLE ${AppConstants.usersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        is_admin INTEGER NOT NULL DEFAULT 0,
        role_id INTEGER,
        last_login_at TEXT,
        failed_login_attempts INTEGER NOT NULL DEFAULT 0,
        locked_until TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (role_id) REFERENCES ${AppConstants.rolesTable} (id)
      )
    ''');

    // جدول جلسات المستخدمين
    await db.execute('''
      CREATE TABLE ${AppConstants.userSessionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_id TEXT NOT NULL UNIQUE,
        session_type TEXT NOT NULL,
        ip_address TEXT,
        user_agent TEXT,
        started_at TEXT NOT NULL,
        ended_at TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جداول الميزات المتقدمة
    await _createAdvancedFeatureTables(db);

    // إدراج البيانات الأولية
    await _insertInitialData(db);
  }

  /// إنشاء جداول الميزات المتقدمة
  Future<void> _createAdvancedFeatureTables(Database db) async {
    // إنشاء جداول منشئ التقارير المرئي
    final visualReportBuilderService = VisualReportBuilderService();
    await visualReportBuilderService.createTables(db);

    // إنشاء جداول النظام الضريبي السوري
    final syrianTaxService = SyrianTaxService();
    await syrianTaxService.createTables(db);

    // إنشاء جداول نظام التنبيهات الذكية
    final smartNotificationService = SmartNotificationService();
    await smartNotificationService.createTables(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    LoggingService.info(
      'ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion',
      category: 'Database',
    );

    // ترقية من الإصدار 1 إلى 2 - إضافة جدول سجل المراجعة
    if (oldVersion < 2) {
      await db.execute('''
        CREATE TABLE ${AppConstants.auditLogTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          action TEXT NOT NULL,
          entity_type TEXT NOT NULL,
          entity_id INTEGER,
          entity_name TEXT,
          old_values TEXT,
          new_values TEXT,
          user_id TEXT,
          user_name TEXT,
          ip_address TEXT,
          user_agent TEXT,
          session_id TEXT,
          timestamp TEXT NOT NULL,
          description TEXT,
          severity TEXT NOT NULL DEFAULT 'INFO',
          category TEXT,
          reference_type TEXT,
          reference_id INTEGER,
          created_at TEXT NOT NULL
        )
      ''');

      // إنشاء فهارس جدول سجل المراجعة
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_action ON ${AppConstants.auditLogTable}(action)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_entity_type ON ${AppConstants.auditLogTable}(entity_type)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_entity_id ON ${AppConstants.auditLogTable}(entity_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON ${AppConstants.auditLogTable}(user_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON ${AppConstants.auditLogTable}(timestamp)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_severity ON ${AppConstants.auditLogTable}(severity)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_category ON ${AppConstants.auditLogTable}(category)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_action_entity ON ${AppConstants.auditLogTable}(action, entity_type)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp_action ON ${AppConstants.auditLogTable}(timestamp, action)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_audit_log_user_timestamp ON ${AppConstants.auditLogTable}(user_id, timestamp)',
      );

      LoggingService.info(
        'تم إنشاء جدول سجل المراجعة بنجاح',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 2 إلى 3 - إضافة نظام المستخدمين والصلاحيات
    if (oldVersion < 3) {
      // جدول الأدوار
      await db.execute('''
        CREATE TABLE ${AppConstants.rolesTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          description TEXT NOT NULL,
          permissions TEXT NOT NULL,
          is_active INTEGER NOT NULL DEFAULT 1,
          is_default INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول المستخدمين
      await db.execute('''
        CREATE TABLE ${AppConstants.usersTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          password_hash TEXT NOT NULL,
          full_name TEXT NOT NULL,
          email TEXT NOT NULL,
          phone TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          is_admin INTEGER NOT NULL DEFAULT 0,
          role_id INTEGER,
          last_login_at TEXT,
          failed_login_attempts INTEGER NOT NULL DEFAULT 0,
          locked_until TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (role_id) REFERENCES ${AppConstants.rolesTable} (id)
        )
      ''');

      // جدول جلسات المستخدمين
      await db.execute('''
        CREATE TABLE ${AppConstants.userSessionsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          session_id TEXT NOT NULL UNIQUE,
          session_type TEXT NOT NULL,
          ip_address TEXT,
          user_agent TEXT,
          started_at TEXT NOT NULL,
          ended_at TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id) ON DELETE CASCADE
        )
      ''');

      // إنشاء فهارس جداول المستخدمين
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_users_username ON ${AppConstants.usersTable}(username)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_users_email ON ${AppConstants.usersTable}(email)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_users_active ON ${AppConstants.usersTable}(is_active)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_users_role ON ${AppConstants.usersTable}(role_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_roles_name ON ${AppConstants.rolesTable}(name)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_roles_active ON ${AppConstants.rolesTable}(is_active)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sessions_user ON ${AppConstants.userSessionsTable}(user_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_sessions_active ON ${AppConstants.userSessionsTable}(is_active)',
      );

      // إدراج الأدوار الافتراضية
      await _insertDefaultRoles(db);

      // إدراج المستخدم الافتراضي
      await _insertDefaultUser(db);

      LoggingService.info(
        'تم إنشاء نظام المستخدمين والصلاحيات بنجاح',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 3 إلى 4 - إضافة نظام الدفعات وحالات الفواتير المتقدم
    if (oldVersion < 4) {
      // جدول الدفعات
      await db.execute('''
        CREATE TABLE ${AppConstants.paymentsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          method TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          payment_date TEXT NOT NULL,
          reference TEXT,
          bank_name TEXT,
          account_number TEXT,
          notes TEXT,
          user_id TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
        )
      ''');

      // جدول سجل تغيير حالات الفواتير
      await db.execute('''
        CREATE TABLE ${AppConstants.invoiceStatusHistoryTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          from_status TEXT NOT NULL,
          to_status TEXT NOT NULL,
          reason TEXT NOT NULL,
          notes TEXT,
          user_id TEXT,
          changed_at TEXT NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
        )
      ''');

      // جدول عروض الأسعار
      await db.execute('''
        CREATE TABLE ${AppConstants.quotationsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          quotation_number TEXT NOT NULL UNIQUE,
          quotation_date TEXT NOT NULL,
          valid_until TEXT,
          customer_id INTEGER,
          supplier_id INTEGER,
          subtotal REAL NOT NULL DEFAULT 0.0,
          tax_amount REAL NOT NULL DEFAULT 0.0,
          discount_amount REAL NOT NULL DEFAULT 0.0,
          total_amount REAL NOT NULL DEFAULT 0.0,
          currency_id INTEGER NOT NULL,
          status TEXT NOT NULL DEFAULT 'draft',
          notes TEXT,
          terms TEXT,
          reference TEXT,
          converted_to_invoice INTEGER,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES ${AppConstants.customersTable} (id),
          FOREIGN KEY (supplier_id) REFERENCES ${AppConstants.suppliersTable} (id),
          FOREIGN KEY (currency_id) REFERENCES ${AppConstants.currenciesTable} (id),
          FOREIGN KEY (converted_to_invoice) REFERENCES ${AppConstants.invoicesTable} (id)
        )
      ''');

      // جدول أصناف عروض الأسعار
      await db.execute('''
        CREATE TABLE ${AppConstants.quotationItemsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          quotation_id INTEGER NOT NULL,
          item_id INTEGER NOT NULL,
          quantity REAL NOT NULL,
          unit_price REAL NOT NULL,
          total_price REAL NOT NULL,
          discount_percentage REAL NOT NULL DEFAULT 0.0,
          discount_amount REAL NOT NULL DEFAULT 0.0,
          tax_percentage REAL NOT NULL DEFAULT 0.0,
          tax_amount REAL NOT NULL DEFAULT 0.0,
          net_amount REAL NOT NULL,
          created_at TEXT NOT NULL,
          FOREIGN KEY (quotation_id) REFERENCES ${AppConstants.quotationsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id)
        )
      ''');

      // جدول الفواتير المتكررة
      await db.execute('''
        CREATE TABLE ${AppConstants.recurringInvoicesTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          template_name TEXT NOT NULL,
          invoice_template TEXT NOT NULL,
          frequency TEXT NOT NULL,
          start_date TEXT NOT NULL,
          end_date TEXT,
          next_generation_date TEXT NOT NULL,
          last_generated_date TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          customer_id INTEGER,
          supplier_id INTEGER,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES ${AppConstants.customersTable} (id),
          FOREIGN KEY (supplier_id) REFERENCES ${AppConstants.suppliersTable} (id)
        )
      ''');

      // إضافة أعمدة جديدة لجدول الفواتير
      await db.execute(
        'ALTER TABLE ${AppConstants.invoicesTable} ADD COLUMN due_date TEXT',
      );
      await db.execute(
        'ALTER TABLE ${AppConstants.invoicesTable} ADD COLUMN terms TEXT',
      );
      await db.execute(
        'ALTER TABLE ${AppConstants.invoicesTable} ADD COLUMN reference TEXT',
      );

      // إنشاء فهارس لتحسين الأداء
      await db.execute(
        'CREATE INDEX idx_payments_invoice_id ON ${AppConstants.paymentsTable} (invoice_id)',
      );
      await db.execute(
        'CREATE INDEX idx_payments_status ON ${AppConstants.paymentsTable} (status)',
      );
      await db.execute(
        'CREATE INDEX idx_payments_date ON ${AppConstants.paymentsTable} (payment_date)',
      );
      await db.execute(
        'CREATE INDEX idx_invoice_status_history_invoice_id ON ${AppConstants.invoiceStatusHistoryTable} (invoice_id)',
      );
      await db.execute(
        'CREATE INDEX idx_quotations_status ON ${AppConstants.quotationsTable} (status)',
      );
      await db.execute(
        'CREATE INDEX idx_quotations_date ON ${AppConstants.quotationsTable} (quotation_date)',
      );
      await db.execute(
        'CREATE INDEX idx_invoices_due_date ON ${AppConstants.invoicesTable} (due_date)',
      );
      await db.execute(
        'CREATE INDEX idx_invoices_status ON ${AppConstants.invoicesTable} (status)',
      );

      LoggingService.info(
        'تم إنشاء جداول نظام الدفعات وحالات الفواتير المتقدم',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 4 إلى 5 - إضافة جدول ربط الدفعات
    if (oldVersion < 5) {
      // جدول ربط الدفعات بالفواتير
      await db.execute('''
        CREATE TABLE ${AppConstants.paymentAllocationsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          payment_id INTEGER NOT NULL,
          invoice_id INTEGER NOT NULL,
          allocated_amount REAL NOT NULL,
          allocation_date TEXT NOT NULL,
          notes TEXT,
          created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (payment_id) REFERENCES ${AppConstants.paymentsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
        )
      ''');

      // إنشاء فهارس لتحسين الأداء
      await db.execute(
        'CREATE INDEX idx_payment_allocations_payment_id ON ${AppConstants.paymentAllocationsTable} (payment_id)',
      );
      await db.execute(
        'CREATE INDEX idx_payment_allocations_invoice_id ON ${AppConstants.paymentAllocationsTable} (invoice_id)',
      );
      await db.execute(
        'CREATE INDEX idx_payment_allocations_date ON ${AppConstants.paymentAllocationsTable} (allocation_date)',
      );

      // ترحيل البيانات الموجودة - ربط الدفعات الحالية بفواتيرها
      await db.execute('''
        INSERT INTO ${AppConstants.paymentAllocationsTable}
        (payment_id, invoice_id, allocated_amount, allocation_date, notes)
        SELECT
          p.id,
          p.invoice_id,
          p.amount,
          p.payment_date,
          'ترحيل تلقائي من النظام القديم'
        FROM ${AppConstants.paymentsTable} p
        WHERE p.status = 'confirmed'
      ''');

      LoggingService.info(
        'تم إنشاء جدول ربط الدفعات وترحيل البيانات الموجودة',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 5 إلى 6 - إضافة جدول قوالب الطباعة
    if (oldVersion < 6) {
      await db.execute('''
        CREATE TABLE ${AppConstants.invoiceTemplatesTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          type TEXT NOT NULL,
          colors TEXT NOT NULL,
          fonts TEXT NOT NULL,
          layout TEXT NOT NULL,
          company_settings TEXT NOT NULL,
          advanced_settings TEXT NOT NULL DEFAULT '{}',
          is_default INTEGER NOT NULL DEFAULT 0,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // إنشاء فهارس لجدول القوالب
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_invoice_templates_type ON ${AppConstants.invoiceTemplatesTable}(type)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_invoice_templates_is_default ON ${AppConstants.invoiceTemplatesTable}(is_default)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_invoice_templates_is_active ON ${AppConstants.invoiceTemplatesTable}(is_active)',
      );

      LoggingService.info('تم إنشاء جدول قوالب الطباعة', category: 'Database');
    }

    // ترقية من الإصدار 6 إلى 7 - إضافة نظام جدولة الدفعات والتذكيرات
    if (oldVersion < 7) {
      // جدول جدولة الدفعات
      await db.execute('''
        CREATE TABLE ${AppConstants.paymentSchedulesTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_id INTEGER NOT NULL,
          schedule_name TEXT NOT NULL,
          total_amount REAL NOT NULL,
          paid_amount REAL NOT NULL DEFAULT 0.0,
          frequency TEXT NOT NULL,
          start_date TEXT NOT NULL,
          end_date TEXT,
          next_payment_date TEXT NOT NULL,
          last_payment_date TEXT,
          installment_amount REAL NOT NULL,
          total_installments INTEGER NOT NULL,
          completed_installments INTEGER NOT NULL DEFAULT 0,
          status TEXT NOT NULL DEFAULT 'active',
          auto_payment INTEGER NOT NULL DEFAULT 0,
          notes TEXT,
          user_id TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
        )
      ''');

      // جدول تذكيرات الجدولة
      await db.execute('''
        CREATE TABLE ${AppConstants.paymentRemindersTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          schedule_id INTEGER NOT NULL,
          type TEXT NOT NULL,
          days_before INTEGER NOT NULL,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (schedule_id) REFERENCES ${AppConstants.paymentSchedulesTable} (id) ON DELETE CASCADE
        )
      ''');

      // جدول إشعارات الدفعات
      await db.execute('''
        CREATE TABLE ${AppConstants.paymentNotificationsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          schedule_id INTEGER NOT NULL,
          invoice_id INTEGER NOT NULL,
          type TEXT NOT NULL,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          due_date TEXT NOT NULL,
          is_read INTEGER NOT NULL DEFAULT 0,
          is_sent INTEGER NOT NULL DEFAULT 0,
          sent_at TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (schedule_id) REFERENCES ${AppConstants.paymentSchedulesTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (invoice_id) REFERENCES ${AppConstants.invoicesTable} (id) ON DELETE CASCADE
        )
      ''');

      // إنشاء فهارس للجداول الجديدة
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_schedules_invoice_id ON ${AppConstants.paymentSchedulesTable}(invoice_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_schedules_status ON ${AppConstants.paymentSchedulesTable}(status)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_schedules_next_payment_date ON ${AppConstants.paymentSchedulesTable}(next_payment_date)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_reminders_schedule_id ON ${AppConstants.paymentRemindersTable}(schedule_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_notifications_schedule_id ON ${AppConstants.paymentNotificationsTable}(schedule_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_notifications_due_date ON ${AppConstants.paymentNotificationsTable}(due_date)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_payment_notifications_is_read ON ${AppConstants.paymentNotificationsTable}(is_read)',
      );

      LoggingService.info(
        'تم إنشاء جداول نظام جدولة الدفعات والتذكيرات',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 7 إلى 8 - إضافة نظام المخزون المتقدم
    if (oldVersion < 8) {
      // جدول المخازن
      await db.execute('''
        CREATE TABLE ${AppConstants.warehousesTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          description TEXT,
          address TEXT,
          manager_name TEXT,
          phone TEXT,
          email TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          is_default INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // جدول المواقع داخل المخازن
      await db.execute('''
        CREATE TABLE ${AppConstants.locationsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          warehouse_id INTEGER NOT NULL,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          aisle TEXT,
          shelf TEXT,
          bin TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (warehouse_id) REFERENCES ${AppConstants.warehousesTable} (id) ON DELETE CASCADE,
          UNIQUE(warehouse_id, code)
        )
      ''');

      // جدول حركات المخزون
      await db.execute('''
        CREATE TABLE ${AppConstants.stockMovementsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          warehouse_id INTEGER NOT NULL,
          location_id INTEGER,
          movement_type TEXT NOT NULL,
          quantity REAL NOT NULL,
          unit_cost REAL,
          total_cost REAL,
          reference_type TEXT,
          reference_id INTEGER,
          reference_number TEXT,
          notes TEXT,
          movement_date TEXT NOT NULL,
          user_id INTEGER,
          created_at TEXT NOT NULL,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (warehouse_id) REFERENCES ${AppConstants.warehousesTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (location_id) REFERENCES ${AppConstants.locationsTable} (id) ON DELETE SET NULL,
          FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL
        )
      ''');

      // جدول جرد المخزون
      await db.execute('''
        CREATE TABLE ${AppConstants.inventoryCountsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          count_number TEXT NOT NULL UNIQUE,
          warehouse_id INTEGER NOT NULL,
          count_date TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'draft',
          notes TEXT,
          counted_by INTEGER,
          approved_by INTEGER,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (warehouse_id) REFERENCES ${AppConstants.warehousesTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (counted_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL,
          FOREIGN KEY (approved_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL
        )
      ''');

      // جدول تفاصيل جرد المخزون
      await db.execute('''
        CREATE TABLE ${AppConstants.inventoryCountItemsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          inventory_count_id INTEGER NOT NULL,
          item_id INTEGER NOT NULL,
          location_id INTEGER,
          system_quantity REAL NOT NULL DEFAULT 0,
          counted_quantity REAL NOT NULL DEFAULT 0,
          variance_quantity REAL NOT NULL DEFAULT 0,
          unit_cost REAL,
          variance_value REAL,
          notes TEXT,
          created_at TEXT NOT NULL,
          FOREIGN KEY (inventory_count_id) REFERENCES ${AppConstants.inventoryCountsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (location_id) REFERENCES ${AppConstants.locationsTable} (id) ON DELETE SET NULL,
          UNIQUE(inventory_count_id, item_id, location_id)
        )
      ''');

      // جدول مخزون الأصناف في المواقع
      await db.execute('''
        CREATE TABLE ${AppConstants.itemLocationsTable} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          warehouse_id INTEGER NOT NULL,
          location_id INTEGER,
          quantity REAL NOT NULL DEFAULT 0,
          reserved_quantity REAL NOT NULL DEFAULT 0,
          available_quantity REAL NOT NULL DEFAULT 0,
          min_quantity REAL NOT NULL DEFAULT 0,
          max_quantity REAL NOT NULL DEFAULT 0,
          reorder_point REAL NOT NULL DEFAULT 0,
          last_movement_date TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (warehouse_id) REFERENCES ${AppConstants.warehousesTable} (id) ON DELETE CASCADE,
          FOREIGN KEY (location_id) REFERENCES ${AppConstants.locationsTable} (id) ON DELETE SET NULL,
          UNIQUE(item_id, warehouse_id, location_id)
        )
      ''');

      // إنشاء فهارس للجداول الجديدة
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouses_code ON ${AppConstants.warehousesTable}(code)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_warehouses_active ON ${AppConstants.warehousesTable}(is_active)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_locations_warehouse_id ON ${AppConstants.locationsTable}(warehouse_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_locations_code ON ${AppConstants.locationsTable}(code)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_stock_movements_item_id ON ${AppConstants.stockMovementsTable}(item_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_stock_movements_warehouse_id ON ${AppConstants.stockMovementsTable}(warehouse_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON ${AppConstants.stockMovementsTable}(movement_date)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON ${AppConstants.stockMovementsTable}(movement_type)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_counts_warehouse_id ON ${AppConstants.inventoryCountsTable}(warehouse_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_counts_status ON ${AppConstants.inventoryCountsTable}(status)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_count_items_count_id ON ${AppConstants.inventoryCountItemsTable}(inventory_count_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_locations_item_id ON ${AppConstants.itemLocationsTable}(item_id)',
      );
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_item_locations_warehouse_id ON ${AppConstants.itemLocationsTable}(warehouse_id)',
      );

      // إدراج مخزن افتراضي
      final now = DateTime.now().toIso8601String();
      await db.insert(AppConstants.warehousesTable, {
        'code': 'WH001',
        'name': 'المخزن الرئيسي',
        'description': 'المخزن الافتراضي للنظام',
        'is_active': 1,
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      });

      // إدراج موقع افتراضي
      final warehouseResult = await db.query(
        AppConstants.warehousesTable,
        where: 'code = ?',
        whereArgs: ['WH001'],
        limit: 1,
      );

      if (warehouseResult.isNotEmpty) {
        final warehouseId = warehouseResult.first['id'] as int;
        await db.insert(AppConstants.locationsTable, {
          'warehouse_id': warehouseId,
          'code': 'LOC001',
          'name': 'الموقع الافتراضي',
          'description': 'الموقع الافتراضي في المخزن الرئيسي',
          'is_active': 1,
          'created_at': now,
          'updated_at': now,
        });
      }

      LoggingService.info(
        'تم إنشاء جداول نظام المخزون المتقدم',
        category: 'Database',
      );
    }

    // ترقية من الإصدار 8 إلى 9 - إضافة نظام الموارد البشرية (HR)
    if (oldVersion < 9) {
      await _createHRTables(db);

      LoggingService.info(
        'تم إنشاء جداول نظام الموارد البشرية',
        category: 'Database',
      );
    }
  }

  /// إنشاء جداول نظام الموارد البشرية
  Future<void> _createHRTables(Database db) async {
    // جدول الأقسام
    await db.execute('''
      CREATE TABLE ${AppConstants.departmentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        manager_id INTEGER,
        cost_center_account_id INTEGER,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (manager_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (cost_center_account_id) REFERENCES ${AppConstants.accountsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول المناصب
    await db.execute('''
      CREATE TABLE ${AppConstants.positionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        title TEXT NOT NULL,
        description TEXT,
        department_id INTEGER,
        min_salary REAL NOT NULL DEFAULT 0,
        max_salary REAL NOT NULL DEFAULT 0,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول الموظفين
    await db.execute('''
      CREATE TABLE ${AppConstants.employeesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_number TEXT NOT NULL UNIQUE,
        national_id TEXT NOT NULL UNIQUE,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        full_name TEXT NOT NULL,
        date_of_birth TEXT,
        gender TEXT,
        marital_status TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        emergency_contact_name TEXT,
        emergency_contact_phone TEXT,
        department_id INTEGER,
        position_id INTEGER,
        hire_date TEXT NOT NULL,
        termination_date TEXT,
        status TEXT NOT NULL DEFAULT '${AppConstants.employeeStatusActive}',
        basic_salary REAL NOT NULL DEFAULT 0,
        cost_center_account_id INTEGER,
        bank_account_number TEXT,
        bank_name TEXT,
        social_insurance_number TEXT,
        tax_number TEXT,
        photo_path TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (position_id) REFERENCES ${AppConstants.positionsTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (cost_center_account_id) REFERENCES ${AppConstants.accountsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول عقود الموظفين
    await db.execute('''
      CREATE TABLE ${AppConstants.employeeContractsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        contract_number TEXT NOT NULL UNIQUE,
        contract_type TEXT NOT NULL DEFAULT '${AppConstants.contractTypePermanent}',
        start_date TEXT NOT NULL,
        end_date TEXT,
        basic_salary REAL NOT NULL,
        allowances REAL NOT NULL DEFAULT 0,
        working_hours_per_day INTEGER NOT NULL DEFAULT 8,
        working_days_per_week INTEGER NOT NULL DEFAULT 5,
        annual_leave_days INTEGER NOT NULL DEFAULT 21,
        probation_period_months INTEGER NOT NULL DEFAULT 3,
        notice_period_days INTEGER NOT NULL DEFAULT 30,
        contract_terms TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
      )
    ''');

    // جدول الحضور والانصراف
    await db.execute('''
      CREATE TABLE ${AppConstants.attendanceTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        attendance_date TEXT NOT NULL,
        check_in_time TEXT,
        check_out_time TEXT,
        break_start_time TEXT,
        break_end_time TEXT,
        total_hours REAL NOT NULL DEFAULT 0,
        regular_hours REAL NOT NULL DEFAULT 0,
        overtime_hours REAL NOT NULL DEFAULT 0,
        late_minutes INTEGER NOT NULL DEFAULT 0,
        early_leave_minutes INTEGER NOT NULL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'present',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        UNIQUE(employee_id, attendance_date)
      )
    ''');

    // جدول الإجازات
    await db.execute('''
      CREATE TABLE ${AppConstants.leavesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT NOT NULL DEFAULT '${AppConstants.leaveTypeAnnual}',
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        total_days INTEGER NOT NULL,
        reason TEXT,
        status TEXT NOT NULL DEFAULT '${AppConstants.leaveStatusPending}',
        requested_by INTEGER,
        approved_by INTEGER,
        approved_at TEXT,
        rejection_reason TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (requested_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (approved_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول القروض والسلف
    await db.execute('''
      CREATE TABLE ${AppConstants.loansTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        loan_number TEXT NOT NULL UNIQUE,
        loan_type TEXT NOT NULL DEFAULT '${AppConstants.loanTypeAdvance}',
        amount REAL NOT NULL,
        remaining_amount REAL NOT NULL,
        monthly_installment REAL NOT NULL,
        installments_count INTEGER NOT NULL,
        paid_installments INTEGER NOT NULL DEFAULT 0,
        interest_rate REAL NOT NULL DEFAULT 0,
        start_date TEXT NOT NULL,
        end_date TEXT,
        status TEXT NOT NULL DEFAULT '${AppConstants.loanStatusActive}',
        purpose TEXT,
        approved_by INTEGER,
        approved_at TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (approved_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول أقساط القروض
    await db.execute('''
      CREATE TABLE ${AppConstants.loanInstallmentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loan_id INTEGER NOT NULL,
        installment_number INTEGER NOT NULL,
        due_date TEXT NOT NULL,
        amount REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0,
        paid_date TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (loan_id) REFERENCES ${AppConstants.loansTable} (id) ON DELETE CASCADE,
        UNIQUE(loan_id, installment_number)
      )
    ''');

    // جدول مستندات الموظفين
    await db.execute('''
      CREATE TABLE ${AppConstants.employeeDocumentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        document_type TEXT NOT NULL,
        document_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER,
        upload_date TEXT NOT NULL,
        expiry_date TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
      )
    ''');

    // جدول كشوف الرواتب
    await db.execute('''
      CREATE TABLE ${AppConstants.payrollTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        payroll_number TEXT NOT NULL UNIQUE,
        month INTEGER NOT NULL,
        year INTEGER NOT NULL,
        pay_date TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'draft',
        total_basic_salary REAL NOT NULL DEFAULT 0,
        total_allowances REAL NOT NULL DEFAULT 0,
        total_deductions REAL NOT NULL DEFAULT 0,
        total_tax REAL NOT NULL DEFAULT 0,
        total_insurance REAL NOT NULL DEFAULT 0,
        total_net_salary REAL NOT NULL DEFAULT 0,
        notes TEXT,
        created_by INTEGER,
        approved_by INTEGER,
        approved_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (created_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL,
        FOREIGN KEY (approved_by) REFERENCES ${AppConstants.usersTable} (id) ON DELETE SET NULL,
        UNIQUE(month, year)
      )
    ''');

    // جدول تفاصيل كشوف الرواتب
    await db.execute('''
      CREATE TABLE ${AppConstants.payrollDetailsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        payroll_id INTEGER NOT NULL,
        employee_id INTEGER NOT NULL,
        basic_salary REAL NOT NULL DEFAULT 0,
        allowances REAL NOT NULL DEFAULT 0,
        overtime_amount REAL NOT NULL DEFAULT 0,
        bonus_amount REAL NOT NULL DEFAULT 0,
        deductions REAL NOT NULL DEFAULT 0,
        loan_deduction REAL NOT NULL DEFAULT 0,
        tax_amount REAL NOT NULL DEFAULT 0,
        insurance_amount REAL NOT NULL DEFAULT 0,
        net_salary REAL NOT NULL DEFAULT 0,
        working_days INTEGER NOT NULL DEFAULT 0,
        actual_working_days INTEGER NOT NULL DEFAULT 0,
        overtime_hours REAL NOT NULL DEFAULT 0,
        late_hours REAL NOT NULL DEFAULT 0,
        absence_days INTEGER NOT NULL DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (payroll_id) REFERENCES ${AppConstants.payrollTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        UNIQUE(payroll_id, employee_id)
      )
    ''');

    // جدول تفاصيل عناصر الراتب
    await db.execute('''
      CREATE TABLE ${AppConstants.salaryDetailsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        component_type TEXT NOT NULL,
        component_name TEXT NOT NULL,
        amount REAL NOT NULL,
        is_percentage INTEGER NOT NULL DEFAULT 0,
        percentage_of TEXT,
        is_taxable INTEGER NOT NULL DEFAULT 1,
        is_active INTEGER NOT NULL DEFAULT 1,
        effective_from TEXT NOT NULL,
        effective_to TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE
      )
    ''');

    // جدول قوالب الرواتب
    await db.execute('''
      CREATE TABLE ${AppConstants.salaryTemplatesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        basic_salary REAL NOT NULL DEFAULT 0,
        components TEXT, -- JSON array of salary components
        is_active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول مكونات الراتب
    await db.execute('''
      CREATE TABLE ${AppConstants.salaryComponentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL CHECK (type IN ('allowance', 'deduction', 'bonus')),
        description TEXT,
        default_amount REAL NOT NULL DEFAULT 0,
        is_percentage INTEGER NOT NULL DEFAULT 0,
        percentage_of TEXT,
        is_taxable INTEGER NOT NULL DEFAULT 1,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول برامج التدريب
    await db.execute('''
      CREATE TABLE ${AppConstants.trainingProgramsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        duration_hours INTEGER NOT NULL DEFAULT 0,
        cost REAL NOT NULL DEFAULT 0,
        category TEXT,
        prerequisites TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول جلسات التدريب
    await db.execute('''
      CREATE TABLE ${AppConstants.trainingSessionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER NOT NULL,
        session_name TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        trainer_name TEXT,
        trainer_email TEXT,
        location TEXT,
        max_participants INTEGER NOT NULL DEFAULT 0,
        current_participants INTEGER NOT NULL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'scheduled',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (program_id) REFERENCES ${AppConstants.trainingProgramsTable} (id) ON DELETE CASCADE
      )
    ''');

    // جدول تسجيلات التدريب
    await db.execute('''
      CREATE TABLE ${AppConstants.trainingEnrollmentsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER NOT NULL,
        employee_id INTEGER NOT NULL,
        enrollment_date TEXT NOT NULL,
        completion_date TEXT,
        status TEXT NOT NULL DEFAULT 'enrolled',
        score REAL,
        certificate_issued INTEGER NOT NULL DEFAULT 0,
        feedback TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (session_id) REFERENCES ${AppConstants.trainingSessionsTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        UNIQUE(session_id, employee_id)
      )
    ''');

    // جدول دورات تقييم الأداء
    await db.execute('''
      CREATE TABLE ${AppConstants.performanceCyclesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cycle_name TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'active',
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول معايير التقييم
    await db.execute('''
      CREATE TABLE ${AppConstants.evaluationCriteriaTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cycle_id INTEGER NOT NULL,
        criteria_name TEXT NOT NULL,
        weight REAL NOT NULL DEFAULT 1.0,
        max_score REAL NOT NULL DEFAULT 100,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (cycle_id) REFERENCES ${AppConstants.performanceCyclesTable} (id) ON DELETE CASCADE
      )
    ''');

    // جدول تقييمات الأداء
    await db.execute('''
      CREATE TABLE ${AppConstants.performanceEvaluationsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cycle_id INTEGER NOT NULL,
        employee_id INTEGER NOT NULL,
        evaluator_id INTEGER NOT NULL,
        total_score REAL NOT NULL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'draft',
        self_evaluation TEXT,
        manager_comments TEXT,
        goals_next_period TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (cycle_id) REFERENCES ${AppConstants.performanceCyclesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (evaluator_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL,
        UNIQUE(cycle_id, employee_id)
      )
    ''');

    // جدول تفاصيل تقييم الأداء
    await db.execute('''
      CREATE TABLE ${AppConstants.evaluationDetailsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        evaluation_id INTEGER NOT NULL,
        criteria_id INTEGER NOT NULL,
        score REAL NOT NULL DEFAULT 0,
        comments TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (evaluation_id) REFERENCES ${AppConstants.performanceEvaluationsTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (criteria_id) REFERENCES ${AppConstants.evaluationCriteriaTable} (id) ON DELETE CASCADE,
        UNIQUE(evaluation_id, criteria_id)
      )
    ''');

    // جدول المسارات الوظيفية
    await db.execute('''
      CREATE TABLE ${AppConstants.careerPathsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        path_name TEXT NOT NULL,
        description TEXT,
        department_id INTEGER,
        levels TEXT, -- JSON array of career levels
        requirements TEXT, -- JSON array of requirements
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (department_id) REFERENCES ${AppConstants.departmentsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول خطط التطوير الوظيفي
    await db.execute('''
      CREATE TABLE ${AppConstants.careerDevelopmentPlansTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        career_path_id INTEGER,
        current_level TEXT,
        target_level TEXT,
        target_date TEXT,
        development_goals TEXT, -- JSON array of goals
        progress_notes TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (career_path_id) REFERENCES ${AppConstants.careerPathsTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول الموافقات
    await db.execute('''
      CREATE TABLE approvals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        request_type TEXT NOT NULL CHECK (request_type IN ('leave', 'loan', 'overtime', 'expense', 'training', 'other')),
        request_id INTEGER NOT NULL,
        employee_id INTEGER NOT NULL,
        approver_id INTEGER,
        status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
        priority TEXT NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        title TEXT NOT NULL,
        description TEXT,
        request_data TEXT, -- JSON data for the specific request
        approval_notes TEXT,
        rejection_reason TEXT,
        requested_at TEXT NOT NULL,
        approved_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (approver_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL
      )
    ''');

    // جدول الوثائق
    await db.execute('''
      CREATE TABLE employee_documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        document_type TEXT NOT NULL CHECK (document_type IN ('contract', 'id_copy', 'certificate', 'photo', 'cv', 'medical', 'other')),
        document_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL DEFAULT 0,
        mime_type TEXT,
        description TEXT,
        is_confidential INTEGER NOT NULL DEFAULT 0,
        expiry_date TEXT,
        uploaded_by INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES ${AppConstants.employeesTable} (id) ON DELETE SET NULL
      )
    ''');

    // إنشاء فهارس للجداول الجديدة
    await _createHRIndexes(db);
    await _createAdvancedHRIndexes(db);

    // إدراج بيانات افتراضية
    await _insertDefaultHRData(db);
  }

  /// إنشاء فهارس نظام الموارد البشرية
  Future<void> _createHRIndexes(Database db) async {
    // فهارس جدول الموظفين
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_number ON ${AppConstants.employeesTable}(employee_number)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_national_id ON ${AppConstants.employeesTable}(national_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_department ON ${AppConstants.employeesTable}(department_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_position ON ${AppConstants.employeesTable}(position_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_status ON ${AppConstants.employeesTable}(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_employees_hire_date ON ${AppConstants.employeesTable}(hire_date)',
    );

    // فهارس جدول الحضور
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_attendance_employee_id ON ${AppConstants.attendanceTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_attendance_date ON ${AppConstants.attendanceTable}(attendance_date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_attendance_employee_date ON ${AppConstants.attendanceTable}(employee_id, attendance_date)',
    );

    // فهارس جدول الإجازات
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_leaves_employee_id ON ${AppConstants.leavesTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_leaves_status ON ${AppConstants.leavesTable}(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_leaves_type ON ${AppConstants.leavesTable}(leave_type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_leaves_dates ON ${AppConstants.leavesTable}(start_date, end_date)',
    );

    // فهارس جدول القروض
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_loans_employee_id ON ${AppConstants.loansTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_loans_status ON ${AppConstants.loansTable}(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_loans_type ON ${AppConstants.loansTable}(loan_type)',
    );

    // فهارس جدول كشوف الرواتب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_payroll_month_year ON ${AppConstants.payrollTable}(month, year)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_payroll_status ON ${AppConstants.payrollTable}(status)',
    );

    // فهارس جدول تفاصيل كشوف الرواتب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_payroll_details_payroll_id ON ${AppConstants.payrollDetailsTable}(payroll_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_payroll_details_employee_id ON ${AppConstants.payrollDetailsTable}(employee_id)',
    );
  }

  /// إنشاء فهارس الجداول المتقدمة لنظام الموارد البشرية
  Future<void> _createAdvancedHRIndexes(Database db) async {
    // فهارس جدول قوالب الرواتب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_salary_templates_name ON ${AppConstants.salaryTemplatesTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_salary_templates_active ON ${AppConstants.salaryTemplatesTable}(is_active)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_salary_templates_default ON ${AppConstants.salaryTemplatesTable}(is_default)',
    );

    // فهارس جدول برامج التدريب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_programs_name ON ${AppConstants.trainingProgramsTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_programs_category ON ${AppConstants.trainingProgramsTable}(category)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_programs_active ON ${AppConstants.trainingProgramsTable}(is_active)',
    );

    // فهارس جدول جلسات التدريب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_sessions_program_id ON ${AppConstants.trainingSessionsTable}(program_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_sessions_status ON ${AppConstants.trainingSessionsTable}(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_sessions_dates ON ${AppConstants.trainingSessionsTable}(start_date, end_date)',
    );

    // فهارس جدول تسجيلات التدريب
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_enrollments_session_id ON ${AppConstants.trainingEnrollmentsTable}(session_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_enrollments_employee_id ON ${AppConstants.trainingEnrollmentsTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_training_enrollments_status ON ${AppConstants.trainingEnrollmentsTable}(status)',
    );

    // فهارس جدول دورات تقييم الأداء
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_cycles_status ON ${AppConstants.performanceCyclesTable}(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_cycles_dates ON ${AppConstants.performanceCyclesTable}(start_date, end_date)',
    );

    // فهارس جدول معايير التقييم
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_evaluation_criteria_cycle_id ON ${AppConstants.evaluationCriteriaTable}(cycle_id)',
    );

    // فهارس جدول تقييمات الأداء
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_evaluations_cycle_id ON ${AppConstants.performanceEvaluationsTable}(cycle_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_evaluations_employee_id ON ${AppConstants.performanceEvaluationsTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_evaluations_evaluator_id ON ${AppConstants.performanceEvaluationsTable}(evaluator_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_performance_evaluations_status ON ${AppConstants.performanceEvaluationsTable}(status)',
    );

    // فهارس جدول تفاصيل تقييم الأداء
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_evaluation_details_evaluation_id ON ${AppConstants.evaluationDetailsTable}(evaluation_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_evaluation_details_criteria_id ON ${AppConstants.evaluationDetailsTable}(criteria_id)',
    );

    // فهارس جدول المسارات الوظيفية
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_career_paths_department_id ON ${AppConstants.careerPathsTable}(department_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_career_paths_active ON ${AppConstants.careerPathsTable}(is_active)',
    );

    // فهارس جدول خطط التطوير الوظيفي
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_career_development_plans_employee_id ON ${AppConstants.careerDevelopmentPlansTable}(employee_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_career_development_plans_career_path_id ON ${AppConstants.careerDevelopmentPlansTable}(career_path_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_career_development_plans_status ON ${AppConstants.careerDevelopmentPlansTable}(status)',
    );
  }

  /// إدراج البيانات الافتراضية لنظام الموارد البشرية
  Future<void> _insertDefaultHRData(Database db) async {
    final now = DateTime.now().toIso8601String();

    // إدراج قسم افتراضي
    await db.insert(AppConstants.departmentsTable, {
      'code': 'ADMIN',
      'name': 'الإدارة العامة',
      'description': 'القسم الإداري الرئيسي',
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
    });

    // إدراج منصب افتراضي
    final departmentResult = await db.query(
      AppConstants.departmentsTable,
      where: 'code = ?',
      whereArgs: ['ADMIN'],
      limit: 1,
    );

    if (departmentResult.isNotEmpty) {
      final departmentId = departmentResult.first['id'] as int;
      await db.insert(AppConstants.positionsTable, {
        'code': 'MGR',
        'title': 'مدير',
        'description': 'منصب إداري',
        'department_id': departmentId,
        'min_salary': 1000000,
        'max_salary': 5000000,
        'is_active': 1,
        'created_at': now,
        'updated_at': now,
      });

      // إدراج قالب راتب افتراضي
      await db.insert(AppConstants.salaryTemplatesTable, {
        'name': 'قالب الموظف الجديد',
        'description': 'قالب راتب افتراضي للموظفين الجدد',
        'basic_salary': 500000,
        'components': jsonEncode([
          {
            'type': 'allowance',
            'name': 'بدل نقل',
            'amount': 50000,
            'is_percentage': false,
          },
          {
            'type': 'deduction',
            'name': 'ضمان اجتماعي',
            'amount': 7,
            'is_percentage': true,
            'percentage_of': 'basic_salary',
          },
        ]),
        'is_active': 1,
        'is_default': 1,
        'created_at': now,
        'updated_at': now,
      });

      // إدراج برنامج تدريب افتراضي
      await db.insert(AppConstants.trainingProgramsTable, {
        'name': 'التوجيه للموظفين الجدد',
        'description': 'برنامج تدريبي للموظفين الجدد للتعريف بالشركة وسياساتها',
        'duration_hours': 8,
        'cost': 0,
        'category': 'orientation',
        'is_active': 1,
        'created_at': now,
        'updated_at': now,
      });

      // إدراج دورة تقييم أداء افتراضية
      await db.insert(AppConstants.performanceCyclesTable, {
        'cycle_name': 'دورة التقييم السنوية 2024',
        'start_date': '2024-01-01',
        'end_date': '2024-12-31',
        'status': 'active',
        'description': 'دورة التقييم السنوية للأداء',
        'created_at': now,
        'updated_at': now,
      });

      // إدراج مسار وظيفي افتراضي
      await db.insert(AppConstants.careerPathsTable, {
        'path_name': 'المسار الإداري',
        'description': 'مسار وظيفي للمناصب الإدارية',
        'department_id': departmentId,
        'levels': jsonEncode([
          {
            'level': 1,
            'title': 'موظف',
            'requirements': ['خبرة سنة واحدة'],
          },
          {
            'level': 2,
            'title': 'موظف أول',
            'requirements': ['خبرة 3 سنوات'],
          },
          {
            'level': 3,
            'title': 'مشرف',
            'requirements': ['خبرة 5 سنوات', 'دورة إدارية'],
          },
          {
            'level': 4,
            'title': 'مدير',
            'requirements': ['خبرة 8 سنوات', 'شهادة إدارة'],
          },
        ]),
        'requirements': jsonEncode([
          'شهادة جامعية',
          'مهارات تواصل جيدة',
          'إتقان الحاسوب',
        ]),
        'is_active': 1,
        'created_at': now,
        'updated_at': now,
      });

      LoggingService.info(
        'تم إدراج البيانات الافتراضية المتقدمة لنظام الموارد البشرية',
      );
    }
  }

  Future<void> _insertInitialData(Database db) async {
    String now = DateTime.now().toIso8601String();

    // إدراج العملات الأساسية
    await db.insert(AppConstants.currenciesTable, {
      'code': AppConstants.currencyCodeSYP,
      'name': 'ليرة سورية',
      'symbol': 'ل.س',
      'exchange_rate': 1.0,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    await db.insert(AppConstants.currenciesTable, {
      'code': AppConstants.currencyCodeUSD,
      'name': 'دولار أمريكي',
      'symbol': '\$',
      'exchange_rate': 13000.0,
      'is_default': 0,
      'created_at': now,
      'updated_at': now,
    });

    // إدراج الإعدادات الأساسية
    await db.insert(AppConstants.settingsTable, {
      'key': 'default_currency',
      'value': AppConstants.defaultCurrency,
      'updated_at': now,
    });

    await db.insert(AppConstants.settingsTable, {
      'key': 'company_name',
      'value': 'شركتي',
      'updated_at': now,
    });

    await db.insert(AppConstants.settingsTable, {
      'key': 'language',
      'value': AppConstants.defaultLanguage,
      'updated_at': now,
    });

    // إدراج الحسابات الأساسية
    await _insertBasicAccounts(db, now);

    // إنشاء الفهارس لتحسين الأداء
    await _createIndexes(db);
  }

  // إنشاء فهارس قاعدة البيانات لتحسين الأداء
  Future<void> _createIndexes(Database db) async {
    // فهارس جدول العملاء
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_code ON ${AppConstants.customersTable}(code)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON ${AppConstants.customersTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_active ON ${AppConstants.customersTable}(is_active)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_balance ON ${AppConstants.customersTable}(balance)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_active_balance ON ${AppConstants.customersTable}(is_active, balance)',
    );

    // فهارس جدول الموردين
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_suppliers_code ON ${AppConstants.suppliersTable}(code)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_suppliers_name ON ${AppConstants.suppliersTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_suppliers_active ON ${AppConstants.suppliersTable}(is_active)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_suppliers_balance ON ${AppConstants.suppliersTable}(balance)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_suppliers_active_balance ON ${AppConstants.suppliersTable}(is_active, balance)',
    );

    // فهارس جدول الأصناف
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_items_code ON ${AppConstants.itemsTable}(code)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_items_name ON ${AppConstants.itemsTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_items_active ON ${AppConstants.itemsTable}(is_active)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_items_quantity ON ${AppConstants.itemsTable}(quantity)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_items_low_stock ON ${AppConstants.itemsTable}(quantity, min_quantity)',
    );

    // فهارس جدول الحسابات
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_code ON ${AppConstants.accountsTable}(code)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_name ON ${AppConstants.accountsTable}(name)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_type ON ${AppConstants.accountsTable}(type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_active ON ${AppConstants.accountsTable}(is_active)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_parent ON ${AppConstants.accountsTable}(parent_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_accounts_type_active ON ${AppConstants.accountsTable}(type, is_active)',
    );

    // فهارس جدول القيود المحاسبية
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_number ON ${AppConstants.journalEntriesTable}(entry_number)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON ${AppConstants.journalEntriesTable}(entry_date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_type ON ${AppConstants.journalEntriesTable}(type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_posted ON ${AppConstants.journalEntriesTable}(is_posted)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_date_posted ON ${AppConstants.journalEntriesTable}(entry_date, is_posted)',
    );

    // فهارس جدول تفاصيل القيود المحاسبية
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entry_details_entry_id ON ${AppConstants.journalEntryDetailsTable}(journal_entry_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entry_details_account_id ON ${AppConstants.journalEntryDetailsTable}(account_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entry_details_debit ON ${AppConstants.journalEntryDetailsTable}(debit_amount)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_journal_entry_details_credit ON ${AppConstants.journalEntryDetailsTable}(credit_amount)',
    );

    // فهارس جدول الفواتير
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_number ON ${AppConstants.invoicesTable}(invoice_number)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_date ON ${AppConstants.invoicesTable}(invoice_date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_type ON ${AppConstants.invoicesTable}(type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_customer ON ${AppConstants.invoicesTable}(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_supplier ON ${AppConstants.invoicesTable}(supplier_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_status ON ${AppConstants.invoicesTable}(status)',
    );

    // فهارس جدول تفاصيل الفواتير
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON ${AppConstants.invoiceItemsTable}(invoice_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoice_items_item_id ON ${AppConstants.invoiceItemsTable}(item_id)',
    );

    // فهارس جدول سجل المراجعة
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_action ON ${AppConstants.auditLogTable}(action)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_entity_type ON ${AppConstants.auditLogTable}(entity_type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_entity_id ON ${AppConstants.auditLogTable}(entity_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON ${AppConstants.auditLogTable}(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON ${AppConstants.auditLogTable}(timestamp)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_severity ON ${AppConstants.auditLogTable}(severity)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_category ON ${AppConstants.auditLogTable}(category)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_action_entity ON ${AppConstants.auditLogTable}(action, entity_type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp_action ON ${AppConstants.auditLogTable}(timestamp, action)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_audit_log_user_timestamp ON ${AppConstants.auditLogTable}(user_id, timestamp)',
    );
  }

  Future<void> _insertBasicAccounts(Database db, String now) async {
    // الحسابات الرئيسية للأصول
    await db.insert(AppConstants.accountsTable, {
      'code': '1001',
      'name': 'الصندوق',
      'type': AppConstants.accountTypeAsset,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'النقدية في الصندوق',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert(AppConstants.accountsTable, {
      'code': '1002',
      'name': 'البنك',
      'type': AppConstants.accountTypeAsset,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'الحساب البنكي',
      'created_at': now,
      'updated_at': now,
    });

    // الحسابات الرئيسية للخصوم
    await db.insert(AppConstants.accountsTable, {
      'code': '2001',
      'name': 'حسابات دائنة',
      'type': AppConstants.accountTypeLiability,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'الحسابات الدائنة',
      'created_at': now,
      'updated_at': now,
    });

    // حسابات المبيعات
    await db.insert(AppConstants.accountsTable, {
      'code': '4001',
      'name': 'مبيعات',
      'type': AppConstants.accountTypeSale,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'حساب المبيعات',
      'created_at': now,
      'updated_at': now,
    });

    // حسابات المشتريات
    await db.insert(AppConstants.accountsTable, {
      'code': '5001',
      'name': 'مشتريات',
      'type': AppConstants.accountTypePurchase,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'حساب المشتريات',
      'created_at': now,
      'updated_at': now,
    });

    // حسابات المصروفات
    await db.insert(AppConstants.accountsTable, {
      'code': '6001',
      'name': 'مصروفات عمومية',
      'type': AppConstants.accountTypeExpense,
      'level': 1,
      'is_active': 1,
      'balance': 0.0,
      'currency_id': 1,
      'description': 'المصروفات العمومية',
      'created_at': now,
      'updated_at': now,
    });
  }

  /// إدراج الأدوار الافتراضية
  Future<void> _insertDefaultRoles(Database db) async {
    final now = DateTime.now().toIso8601String();

    // دور المدير العام
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.roleAdmin,
      'description': 'صلاحيات كاملة لجميع وظائف النظام',
      'permissions':
          'viewAccounts,addAccounts,editAccounts,deleteAccounts,viewJournalEntries,addJournalEntries,editJournalEntries,deleteJournalEntries,postJournalEntries,unpostJournalEntries,viewInvoices,addInvoices,editInvoices,deleteInvoices,postInvoices,viewCustomers,addCustomers,editCustomers,deleteCustomers,viewSuppliers,addSuppliers,editSuppliers,deleteSuppliers,viewItems,addItems,editItems,deleteItems,viewReports,exportReports,viewFinancialReports,viewInventoryReports,viewCustomerReports,viewSupplierReports,manageUsers,manageRoles,managePermissions,manageSettings,manageBackup,viewAuditLog,systemAdmin,databaseAdmin',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // دور المحاسب
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.roleAccountant,
      'description': 'صلاحيات المحاسبة الأساسية',
      'permissions':
          'viewAccounts,addAccounts,editAccounts,viewJournalEntries,addJournalEntries,editJournalEntries,postJournalEntries,viewReports,exportReports,viewFinancialReports',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // دور أمين المخزن
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.roleWarehouseKeeper,
      'description': 'صلاحيات إدارة المخزون والأصناف',
      'permissions':
          'viewItems,addItems,editItems,viewInventoryReports,viewInvoices,addInvoices,editInvoices',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // دور موظف المبيعات
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.roleSalesEmployee,
      'description': 'صلاحيات إدارة العملاء والمبيعات',
      'permissions':
          'viewCustomers,addCustomers,editCustomers,viewInvoices,addInvoices,editInvoices,viewItems,viewCustomerReports',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // دور موظف المشتريات
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.rolePurchaseEmployee,
      'description': 'صلاحيات إدارة الموردين والمشتريات',
      'permissions':
          'viewSuppliers,addSuppliers,editSuppliers,viewInvoices,addInvoices,editInvoices,viewItems,viewSupplierReports',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });

    // دور مستخدم عادي
    await db.insert(AppConstants.rolesTable, {
      'name': AppConstants.roleUser,
      'description': 'صلاحيات العرض فقط',
      'permissions':
          'viewAccounts,viewJournalEntries,viewCustomers,viewSuppliers,viewItems,viewInvoices,viewReports',
      'is_active': 1,
      'is_default': 1,
      'created_at': now,
      'updated_at': now,
    });
  }

  /// إدراج المستخدم الافتراضي
  Future<void> _insertDefaultUser(Database db) async {
    final now = DateTime.now().toIso8601String();

    // الحصول على معرف دور المدير العام
    final adminRoleResult = await db.query(
      AppConstants.rolesTable,
      where: 'name = ?',
      whereArgs: [AppConstants.roleAdmin],
      limit: 1,
    );

    int? adminRoleId;
    if (adminRoleResult.isNotEmpty) {
      adminRoleId = adminRoleResult.first['id'] as int;
    }

    // إنشاء hash لكلمة المرور الافتراضية
    // في التطبيق الحقيقي، يجب استخدام bcrypt أو مكتبة تشفير قوية
    final passwordHash = AppConstants.defaultAdminPassword.hashCode.toString();

    await db.insert(AppConstants.usersTable, {
      'username': AppConstants.defaultAdminUsername,
      'password_hash': passwordHash,
      'full_name': AppConstants.defaultAdminFullName,
      'email': AppConstants.defaultAdminEmail,
      'is_active': 1,
      'is_admin': 1,
      'role_id': adminRoleId,
      'failed_login_attempts': 0,
      'created_at': now,
      'updated_at': now,
    });
  }

  /// تنفيذ استعلام مع مراقبة الأداء
  Future<List<Map<String, dynamic>>> executeQueryWithPerformance(
    String query, [
    List<dynamic>? arguments,
  ]) async {
    final stopwatch = Stopwatch()..start();

    try {
      final db = await database;
      final result = await db.rawQuery(query, arguments);

      stopwatch.stop();

      LoggingService.info(
        'تم تنفيذ الاستعلام',
        category: 'DatabasePerformance',
        data: {
          'query': query.length > 100 ? '${query.substring(0, 100)}...' : query,
          'executionTimeMs': stopwatch.elapsedMilliseconds,
          'resultCount': result.length,
        },
      );

      return result;
    } catch (e) {
      stopwatch.stop();

      LoggingService.error(
        'خطأ في تنفيذ الاستعلام',
        category: 'DatabasePerformance',
        data: {
          'query': query,
          'error': e.toString(),
          'executionTimeMs': stopwatch.elapsedMilliseconds,
        },
      );

      rethrow;
    }
  }

  /// تنفيذ معاملة مع مراقبة الأداء
  Future<T> executeTransactionWithPerformance<T>(
    Future<T> Function(Transaction txn) action,
  ) async {
    final stopwatch = Stopwatch()..start();

    try {
      final db = await database;
      final result = await db.transaction(action);

      stopwatch.stop();

      LoggingService.info(
        'تم تنفيذ المعاملة بنجاح',
        category: 'DatabasePerformance',
        data: {'executionTimeMs': stopwatch.elapsedMilliseconds},
      );

      return result;
    } catch (e) {
      stopwatch.stop();

      LoggingService.error(
        'خطأ في تنفيذ المعاملة',
        category: 'DatabasePerformance',
        data: {
          'error': e.toString(),
          'executionTimeMs': stopwatch.elapsedMilliseconds,
        },
      );

      rethrow;
    }
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<DatabaseStatistics> getDatabaseStatistics() async {
    try {
      final db = await database;

      // حجم قاعدة البيانات
      final dbPath = db.path;
      final file = File(dbPath);
      final sizeBytes = await file.length();

      // عدد الجداول
      final tablesResult = await db.rawQuery(
        "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
      );
      final tableCount = tablesResult.first['count'] as int;

      // عدد الفهارس
      final indexesResult = await db.rawQuery(
        "SELECT COUNT(*) as count FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'",
      );
      final indexCount = indexesResult.first['count'] as int;

      // إحصائيات الجداول الرئيسية
      final tableStats = <String, int>{};
      final mainTables = [
        AppConstants.accountsTable,
        AppConstants.journalEntriesTable,
        AppConstants.journalEntryDetailsTable,
        AppConstants.customersTable,
        AppConstants.suppliersTable,
        AppConstants.itemsTable,
        AppConstants.invoicesTable,
        AppConstants.usersTable,
      ];

      for (final table in mainTables) {
        final countResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM $table',
        );
        tableStats[table] = countResult.first['count'] as int;
      }

      return DatabaseStatistics(
        sizeBytes: sizeBytes,
        tableCount: tableCount,
        indexCount: indexCount,
        tableStats: tableStats,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات قاعدة البيانات',
        category: 'Database',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحسين قاعدة البيانات
  Future<void> optimizeDatabase() async {
    try {
      final db = await database;

      LoggingService.info('بدء تحسين قاعدة البيانات', category: 'Database');

      // تحديث الإحصائيات
      await db.execute('ANALYZE');

      // ضغط قاعدة البيانات
      await db.execute('VACUUM');

      // إعادة فهرسة
      await db.execute('REINDEX');

      LoggingService.info(
        'تم تحسين قاعدة البيانات بنجاح',
        category: 'Database',
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحسين قاعدة البيانات',
        category: 'Database',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// نسخ احتياطي من قاعدة البيانات
  Future<String?> createBackup() async {
    try {
      final db = await database;
      final dbPath = db.path;

      final directory = await getApplicationDocumentsDirectory();
      final backupFileName =
          'smart_ledger_backup_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.db';
      final backupPath = '${directory.path}/$backupFileName';

      final originalFile = File(dbPath);
      await originalFile.copy(backupPath);

      LoggingService.info(
        'تم إنشاء نسخة احتياطية',
        category: 'Database',
        data: {'backupPath': backupPath},
      );

      return backupPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء النسخة الاحتياطية',
        category: 'Database',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استعادة من نسخة احتياطية
  Future<bool> restoreFromBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // إغلاق قاعدة البيانات الحالية
      await _database?.close();
      _database = null;

      // نسخ ملف النسخة الاحتياطية
      final dbPath = await getDatabasesPath();
      final targetPath = '$dbPath/${AppConstants.databaseName}';

      await backupFile.copy(targetPath);

      LoggingService.info(
        'تم استعادة النسخة الاحتياطية',
        category: 'Database',
        data: {'backupPath': backupPath},
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في استعادة النسخة الاحتياطية',
        category: 'Database',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// فحص سلامة قاعدة البيانات
  Future<DatabaseIntegrityResult> checkDatabaseIntegrity() async {
    try {
      final db = await database;
      final issues = <String>[];

      // فحص سلامة قاعدة البيانات
      final integrityResult = await db.rawQuery('PRAGMA integrity_check');
      if (integrityResult.first.values.first != 'ok') {
        issues.add('فشل فحص سلامة قاعدة البيانات');
      }

      // فحص المفاتيح الخارجية
      final foreignKeyResult = await db.rawQuery('PRAGMA foreign_key_check');
      if (foreignKeyResult.isNotEmpty) {
        issues.add(
          'توجد مشاكل في المفاتيح الخارجية: ${foreignKeyResult.length}',
        );
      }

      // فحص الجداول المطلوبة
      final requiredTables = [
        AppConstants.accountsTable,
        AppConstants.journalEntriesTable,
        AppConstants.journalEntryDetailsTable,
        AppConstants.customersTable,
        AppConstants.suppliersTable,
        AppConstants.itemsTable,
        AppConstants.invoicesTable,
        AppConstants.usersTable,
        AppConstants.rolesTable,
      ];

      for (final table in requiredTables) {
        final tableExists = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [table],
        );
        if (tableExists.isEmpty) {
          issues.add('الجدول المطلوب غير موجود: $table');
        }
      }

      return DatabaseIntegrityResult(
        isHealthy: issues.isEmpty,
        issues: issues,
        checkedAt: DateTime.now(),
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص سلامة قاعدة البيانات',
        category: 'Database',
        data: {'error': e.toString()},
      );

      return DatabaseIntegrityResult(
        isHealthy: false,
        issues: ['خطأ في فحص سلامة قاعدة البيانات: $e'],
        checkedAt: DateTime.now(),
      );
    }
  }
}

/// إحصائيات قاعدة البيانات
class DatabaseStatistics {
  final int sizeBytes;
  final int tableCount;
  final int indexCount;
  final Map<String, int> tableStats;
  final DateTime lastUpdated;

  const DatabaseStatistics({
    required this.sizeBytes,
    required this.tableCount,
    required this.indexCount,
    required this.tableStats,
    required this.lastUpdated,
  });

  double get sizeMB => sizeBytes / (1024 * 1024);

  Map<String, dynamic> toMap() {
    return {
      'sizeBytes': sizeBytes,
      'sizeMB': sizeMB,
      'tableCount': tableCount,
      'indexCount': indexCount,
      'tableStats': tableStats,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// نتيجة فحص سلامة قاعدة البيانات
class DatabaseIntegrityResult {
  final bool isHealthy;
  final List<String> issues;
  final DateTime checkedAt;

  const DatabaseIntegrityResult({
    required this.isHealthy,
    required this.issues,
    required this.checkedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'isHealthy': isHealthy,
      'issues': issues,
      'issueCount': issues.length,
      'checkedAt': checkedAt.toIso8601String(),
    };
  }
}

/// طريقة خاصة لتهيئة قاعدة البيانات للاختبارات
extension DatabaseHelperTesting on DatabaseHelper {
  /// تهيئة قاعدة البيانات للاختبارات بدون تشفير
  Future<bool> initializeDatabaseForTesting() async {
    try {
      // Create in-memory database for testing
      DatabaseHelper._database = await openDatabase(
        ':memory:',
        version: AppConstants.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );

      LoggingService.info(
        'تم تهيئة قاعدة البيانات للاختبارات بنجاح',
        category: 'DatabaseHelper',
      );

      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في تهيئة قاعدة البيانات للاختبارات',
        category: 'DatabaseHelper',
        data: {'error': e.toString()},
      );
      return false;
    }
  }

  /// إغلاق قاعدة البيانات للاختبارات
  Future<void> closeDatabaseForTesting() async {
    if (DatabaseHelper._database != null) {
      await DatabaseHelper._database!.close();
      DatabaseHelper._database = null;
    }
  }
}
