/// حوار إضافة مهارة جديدة للموظف
/// يوفر واجهة شاملة لإضافة وتقييم مهارات الموظفين
library;

import 'package:flutter/material.dart';
import '../models/training_models.dart';
import '../constants/revolutionary_design_colors.dart';

class AddSkillDialog extends StatefulWidget {
  final int employeeId;
  final Function(EmployeeSkill) onSave;

  const AddSkillDialog({
    super.key,
    required this.employeeId,
    required this.onSave,
  });

  @override
  State<AddSkillDialog> createState() => _AddSkillDialogState();
}

class _AddSkillDialogState extends State<AddSkillDialog> {
  final _formKey = GlobalKey<FormState>();
  final _skillNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _certificationPathController = TextEditingController();

  String _selectedCategory = 'تقنية';
  int _selectedLevel = 1;
  DateTime? _acquiredDate;
  DateTime? _lastAssessedDate;
  bool _isVerified = false;

  final List<String> _categories = [
    'تقنية',
    'إدارية',
    'تواصل',
    'قيادة',
    'تحليلية',
    'إبداعية',
    'لغوية',
    'مالية',
    'تسويقية',
    'أخرى',
  ];

  final Map<int, String> _levelLabels = {
    1: 'مبتدئ',
    2: 'متوسط',
    3: 'متقدم',
    4: 'خبير',
    5: 'استثنائي',
  };

  @override
  void dispose() {
    _skillNameController.dispose();
    _descriptionController.dispose();
    _certificationPathController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSkillNameField(),
                      const SizedBox(height: 16),
                      _buildCategoryAndLevel(),
                      const SizedBox(height: 16),
                      _buildDescriptionField(),
                      const SizedBox(height: 16),
                      _buildDatesSection(),
                      const SizedBox(height: 16),
                      _buildCertificationField(),
                      const SizedBox(height: 16),
                      _buildVerificationSwitch(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.star_border,
            color: RevolutionaryColors.damascusSky,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إضافة مهارة جديدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'إضافة مهارة للموظف مع التقييم',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildSkillNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اسم المهارة *',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _skillNameController,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            hintText: 'مثال: البرمجة بـ Flutter',
            prefixIcon: const Icon(Icons.psychology),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال اسم المهارة';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCategoryAndLevel() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الفئة *',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  prefixIcon: const Icon(Icons.category),
                ),
                items: _categories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value ?? 'تقنية';
                  });
                },
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'المستوى *',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<int>(
                value: _selectedLevel,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  prefixIcon: const Icon(Icons.trending_up),
                ),
                items: _levelLabels.entries.map((entry) {
                  return DropdownMenuItem(
                    value: entry.key,
                    child: Row(
                      children: [
                        Text(entry.value),
                        const SizedBox(width: 8),
                        Row(
                          children: List.generate(5, (index) {
                            return Icon(
                              index < entry.key ? Icons.star : Icons.star_border,
                              size: 16,
                              color: Colors.amber,
                            );
                          }),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedLevel = value ?? 1;
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'وصف المهارة',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            hintText: 'وصف تفصيلي للمهارة ومجال استخدامها',
            prefixIcon: const Icon(Icons.description),
          ),
        ),
      ],
    );
  }

  Widget _buildDatesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التواريخ',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تاريخ الاكتساب',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _selectAcquiredDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 12),
                          Text(
                            _acquiredDate != null
                                ? '${_acquiredDate!.day}/${_acquiredDate!.month}/${_acquiredDate!.year}'
                                : 'اختر التاريخ',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'آخر تقييم',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _selectLastAssessedDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 12),
                          Text(
                            _lastAssessedDate != null
                                ? '${_lastAssessedDate!.day}/${_lastAssessedDate!.month}/${_lastAssessedDate!.year}'
                                : 'اختر التاريخ',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCertificationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مسار الشهادة',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _certificationPathController,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            hintText: 'مسار ملف الشهادة أو الرابط',
            prefixIcon: const Icon(Icons.certificate),
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationSwitch() {
    return Row(
      children: [
        Switch(
          value: _isVerified,
          onChanged: (value) {
            setState(() {
              _isVerified = value;
            });
          },
          activeColor: RevolutionaryColors.successGlow,
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مهارة موثقة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Text(
                'تم التحقق من هذه المهارة رسمياً',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _saveSkill,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: const Text('حفظ المهارة', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }

  void _selectAcquiredDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _acquiredDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _acquiredDate = date;
      });
    }
  }

  void _selectLastAssessedDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _lastAssessedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _lastAssessedDate = date;
      });
    }
  }

  void _saveSkill() {
    if (_formKey.currentState!.validate()) {
      final skill = EmployeeSkill(
        employeeId: widget.employeeId,
        skillName: _skillNameController.text.trim(),
        category: _selectedCategory,
        level: _selectedLevel,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        acquiredDate: _acquiredDate,
        lastAssessedDate: _lastAssessedDate,
        certificationPath: _certificationPathController.text.trim().isEmpty
            ? null
            : _certificationPathController.text.trim(),
        isVerified: _isVerified,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(skill);
      Navigator.of(context).pop();
    }
  }
}
